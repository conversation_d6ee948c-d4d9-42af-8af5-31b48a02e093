<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>愤怒的小鸟</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .game-container {
            position: relative;
            border: 3px solid #8B4513;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
            background: #87CEEB;
        }
        
        #gameCanvas {
            display: block;
            cursor: crosshair;
        }
        
        .ui-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
        }
        
        .controls {
            position: absolute;
            bottom: 10px;
            right: 10px;
            z-index: 10;
        }
        
        .btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #FF8C42;
            transform: translateY(-2px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
            z-index: 20;
        }
        
        .level-info {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <canvas id="gameCanvas" width="1200" height="600"></canvas>
        
        <div class="ui-overlay">
            <div>得分: <span id="score">0</span></div>
            <div>小鸟: <span id="birds">3</span></div>
        </div>
        
        <div class="level-info">
            <div>关卡: <span id="level">1</span></div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="game.reset()">重新开始</button>
            <button class="btn" onclick="game.nextLevel()">下一关</button>
        </div>
        
        <div class="game-over" id="gameOver">
            <h2 id="gameOverTitle">游戏结束</h2>
            <p id="gameOverMessage">你的得分: <span id="finalScore">0</span></p>
            <button class="btn" onclick="game.restart()">重新开始</button>
        </div>
    </div>
    
    <script src="js/physics.js"></script>
    <script src="js/bird.js"></script>
    <script src="js/slingshot.js"></script>
    <script src="js/targets.js"></script>
    <script src="js/levels.js"></script>
    <script src="js/effects.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
