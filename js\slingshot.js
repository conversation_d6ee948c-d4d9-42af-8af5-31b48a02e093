// 弹弓类
class Slingshot {
    constructor(x, y) {
        this.position = new Physics.Vector2(x, y);
        this.width = 60;
        this.height = 120;
        this.isDragging = false;
        this.dragStart = new Physics.Vector2(0, 0);
        this.dragCurrent = new Physics.Vector2(0, 0);
        this.maxPullDistance = 100;
        this.currentBird = null;
        this.launchPower = 0;
        this.aimAngle = 0;
        
        // 弹弓的固定点
        this.leftPost = new Physics.Vector2(x + 10, y + 20);
        this.rightPost = new Physics.Vector2(x + 50, y + 20);
        this.centerPoint = new Physics.Vector2(x + 30, y + 30);
    }
    
    // 设置当前小鸟
    setBird(bird) {
        if (bird && !bird.isLaunched) {
            this.currentBird = bird;
            // 将小鸟放置在弹弓中心
            bird.position.x = this.centerPoint.x - bird.width / 2;
            bird.position.y = this.centerPoint.y - bird.height / 2;
        }
    }
    
    // 开始拖拽
    startDrag(mousePos) {
        if (this.currentBird && !this.currentBird.isLaunched) {
            this.isDragging = true;
            this.dragStart = mousePos.copy();
            this.dragCurrent = mousePos.copy();
        }
    }
    
    // 更新拖拽
    updateDrag(mousePos) {
        if (this.isDragging && this.currentBird) {
            this.dragCurrent = mousePos.copy();
            
            // 计算拖拽向量
            const dragVector = this.dragStart.subtract(this.dragCurrent);
            
            // 限制拖拽距离
            const distance = Math.min(dragVector.magnitude(), this.maxPullDistance);
            const direction = dragVector.normalize();
            const limitedDrag = direction.multiply(distance);
            
            // 更新小鸟位置
            const newPos = this.centerPoint.subtract(limitedDrag);
            this.currentBird.position.x = newPos.x - this.currentBird.width / 2;
            this.currentBird.position.y = newPos.y - this.currentBird.height / 2;
            
            // 计算发射力度和角度
            this.launchPower = distance / this.maxPullDistance;
            this.aimAngle = Math.atan2(limitedDrag.y, limitedDrag.x);
        }
    }
    
    // 结束拖拽并发射
    endDrag() {
        if (this.isDragging && this.currentBird && this.launchPower > 0.1) {
            // 计算发射速度
            const power = this.launchPower * 15; // 调整发射力度
            const velocity = new Physics.Vector2(
                Math.cos(this.aimAngle) * power,
                Math.sin(this.aimAngle) * power
            );
            
            // 发射小鸟
            this.currentBird.launch(velocity);
            this.currentBird = null;
        }
        
        this.isDragging = false;
        this.launchPower = 0;
    }
    
    // 获取预测轨迹
    getPredictedTrajectory() {
        if (this.isDragging && this.launchPower > 0.1) {
            const power = this.launchPower * 15;
            const velocity = new Physics.Vector2(
                Math.cos(this.aimAngle) * power,
                Math.sin(this.aimAngle) * power
            );
            
            const startPos = new Physics.Vector2(
                this.currentBird.position.x + this.currentBird.width / 2,
                this.currentBird.position.y + this.currentBird.height / 2
            );
            
            return Physics.calculateTrajectory(startPos, velocity, 30);
        }
        return [];
    }
    
    // 检查鼠标是否在弹弓区域内
    isMouseInRange(mousePos) {
        const distance = mousePos.distance(this.centerPoint);
        return distance <= this.maxPullDistance + 50;
    }
    
    draw(ctx) {
        // 绘制弹弓底座
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.position.x, this.position.y + this.height - 20, this.width, 20);
        
        // 绘制弹弓支柱
        ctx.fillStyle = '#654321';
        ctx.fillRect(this.position.x + 5, this.position.y + 20, 10, this.height - 40);
        ctx.fillRect(this.position.x + this.width - 15, this.position.y + 20, 10, this.height - 40);
        
        // 绘制弹弓顶部
        ctx.fillStyle = '#8B4513';
        ctx.beginPath();
        ctx.arc(this.leftPost.x, this.leftPost.y, 8, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(this.rightPost.x, this.rightPost.y, 8, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制弹弓绳子
        if (this.currentBird && !this.currentBird.isLaunched) {
            const birdCenter = new Physics.Vector2(
                this.currentBird.position.x + this.currentBird.width / 2,
                this.currentBird.position.y + this.currentBird.height / 2
            );
            
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            
            // 左绳子
            ctx.beginPath();
            ctx.moveTo(this.leftPost.x, this.leftPost.y);
            ctx.lineTo(birdCenter.x, birdCenter.y);
            ctx.stroke();
            
            // 右绳子
            ctx.beginPath();
            ctx.moveTo(this.rightPost.x, this.rightPost.y);
            ctx.lineTo(birdCenter.x, birdCenter.y);
            ctx.stroke();
        } else {
            // 绘制空弹弓绳子
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(this.leftPost.x, this.leftPost.y);
            ctx.lineTo(this.centerPoint.x, this.centerPoint.y);
            ctx.lineTo(this.rightPost.x, this.rightPost.y);
            ctx.stroke();
        }
        
        // 绘制瞄准线和轨迹预测
        if (this.isDragging && this.launchPower > 0.1) {
            // 绘制瞄准线
            ctx.strokeStyle = 'rgba(255, 0, 0, 0.7)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            const aimLength = 100;
            const aimEnd = new Physics.Vector2(
                this.centerPoint.x + Math.cos(this.aimAngle) * aimLength,
                this.centerPoint.y + Math.sin(this.aimAngle) * aimLength
            );
            
            ctx.beginPath();
            ctx.moveTo(this.centerPoint.x, this.centerPoint.y);
            ctx.lineTo(aimEnd.x, aimEnd.y);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制轨迹预测
            const trajectory = this.getPredictedTrajectory();
            if (trajectory.length > 1) {
                ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let i = 0; i < trajectory.length; i++) {
                    const point = trajectory[i];
                    if (i === 0) {
                        ctx.moveTo(point.x, point.y);
                    } else {
                        ctx.lineTo(point.x, point.y);
                    }
                }
                ctx.stroke();
                
                // 绘制轨迹点
                ctx.fillStyle = 'rgba(255, 255, 0, 0.7)';
                for (let i = 0; i < trajectory.length; i += 3) {
                    const point = trajectory[i];
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }
        
        // 绘制力度指示器
        if (this.isDragging && this.launchPower > 0) {
            const barWidth = 100;
            const barHeight = 10;
            const barX = this.position.x - 20;
            const barY = this.position.y - 30;
            
            // 背景
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(barX, barY, barWidth, barHeight);
            
            // 力度条
            const powerWidth = barWidth * this.launchPower;
            const powerColor = this.launchPower < 0.5 ? '#00FF00' : 
                              this.launchPower < 0.8 ? '#FFFF00' : '#FF0000';
            ctx.fillStyle = powerColor;
            ctx.fillRect(barX, barY, powerWidth, barHeight);
            
            // 边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 1;
            ctx.strokeRect(barX, barY, barWidth, barHeight);
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('力度: ' + Math.round(this.launchPower * 100) + '%', 
                        barX + barWidth / 2, barY - 5);
        }
    }
    
    // 重置弹弓状态
    reset() {
        this.isDragging = false;
        this.currentBird = null;
        this.launchPower = 0;
        this.aimAngle = 0;
    }
}
