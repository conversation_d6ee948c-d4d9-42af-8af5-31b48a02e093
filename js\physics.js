// 物理引擎基础类
class Physics {
    constructor() {
        this.gravity = 0.5;
        this.friction = 0.98;
        this.airResistance = 0.999;
    }
    
    // 向量类
    static Vector2 = class {
        constructor(x = 0, y = 0) {
            this.x = x;
            this.y = y;
        }
        
        add(vector) {
            return new Physics.Vector2(this.x + vector.x, this.y + vector.y);
        }
        
        subtract(vector) {
            return new Physics.Vector2(this.x - vector.x, this.y - vector.y);
        }
        
        multiply(scalar) {
            return new Physics.Vector2(this.x * scalar, this.y * scalar);
        }
        
        magnitude() {
            return Math.sqrt(this.x * this.x + this.y * this.y);
        }
        
        normalize() {
            const mag = this.magnitude();
            if (mag === 0) return new Physics.Vector2(0, 0);
            return new Physics.Vector2(this.x / mag, this.y / mag);
        }
        
        distance(vector) {
            return Math.sqrt(Math.pow(this.x - vector.x, 2) + Math.pow(this.y - vector.y, 2));
        }
        
        copy() {
            return new Physics.Vector2(this.x, this.y);
        }
    };
    
    // 物理体基类
    static Body = class {
        constructor(x, y, width, height, mass = 1) {
            this.position = new Physics.Vector2(x, y);
            this.velocity = new Physics.Vector2(0, 0);
            this.acceleration = new Physics.Vector2(0, 0);
            this.width = width;
            this.height = height;
            this.mass = mass;
            this.isStatic = false;
            this.restitution = 0.6; // 弹性系数
            this.friction = 0.8;
            this.isDestroyed = false;
            this.health = 100;
            this.maxHealth = 100;
        }
        
        applyForce(force) {
            if (!this.isStatic) {
                const f = force.multiply(1 / this.mass);
                this.acceleration = this.acceleration.add(f);
            }
        }
        
        update() {
            if (!this.isStatic && !this.isDestroyed) {
                // 应用重力
                this.applyForce(new Physics.Vector2(0, this.mass * 0.5));
                
                // 更新速度
                this.velocity = this.velocity.add(this.acceleration);
                
                // 应用空气阻力
                this.velocity = this.velocity.multiply(0.999);
                
                // 更新位置
                this.position = this.position.add(this.velocity);
                
                // 重置加速度
                this.acceleration = new Physics.Vector2(0, 0);
                
                // 地面碰撞检测
                if (this.position.y + this.height > 580) {
                    this.position.y = 580 - this.height;
                    this.velocity.y *= -this.restitution;
                    this.velocity.x *= this.friction;
                    
                    // 如果速度很小，停止运动
                    if (Math.abs(this.velocity.y) < 1) {
                        this.velocity.y = 0;
                    }
                    if (Math.abs(this.velocity.x) < 0.5) {
                        this.velocity.x = 0;
                    }
                }
                
                // 边界检测
                if (this.position.x < 0) {
                    this.position.x = 0;
                    this.velocity.x *= -this.restitution;
                } else if (this.position.x + this.width > 1200) {
                    this.position.x = 1200 - this.width;
                    this.velocity.x *= -this.restitution;
                }
            }
        }
        
        getBounds() {
            return {
                left: this.position.x,
                right: this.position.x + this.width,
                top: this.position.y,
                bottom: this.position.y + this.height,
                centerX: this.position.x + this.width / 2,
                centerY: this.position.y + this.height / 2
            };
        }
        
        takeDamage(damage) {
            this.health -= damage;
            if (this.health <= 0) {
                this.isDestroyed = true;
            }
        }
    };
    
    // 碰撞检测
    static checkCollision(bodyA, bodyB) {
        const boundsA = bodyA.getBounds();
        const boundsB = bodyB.getBounds();
        
        return !(boundsA.right < boundsB.left || 
                boundsA.left > boundsB.right || 
                boundsA.bottom < boundsB.top || 
                boundsA.top > boundsB.bottom);
    }
    
    // 处理碰撞响应
    static resolveCollision(bodyA, bodyB) {
        if (bodyA.isStatic && bodyB.isStatic) return;
        
        const boundsA = bodyA.getBounds();
        const boundsB = bodyB.getBounds();
        
        // 计算重叠区域
        const overlapX = Math.min(boundsA.right - boundsB.left, boundsB.right - boundsA.left);
        const overlapY = Math.min(boundsA.bottom - boundsB.top, boundsB.bottom - boundsA.top);
        
        // 分离物体
        if (overlapX < overlapY) {
            // 水平分离
            const direction = boundsA.centerX < boundsB.centerX ? -1 : 1;
            const separation = overlapX / 2;
            
            if (!bodyA.isStatic) {
                bodyA.position.x += direction * separation;
            }
            if (!bodyB.isStatic) {
                bodyB.position.x -= direction * separation;
            }
            
            // 交换速度
            const relativeVelocity = bodyA.velocity.x - bodyB.velocity.x;
            const impulse = 2 * relativeVelocity / (bodyA.mass + bodyB.mass);
            
            if (!bodyA.isStatic) {
                bodyA.velocity.x -= impulse * bodyB.mass * bodyA.restitution;
            }
            if (!bodyB.isStatic) {
                bodyB.velocity.x += impulse * bodyA.mass * bodyB.restitution;
            }
        } else {
            // 垂直分离
            const direction = boundsA.centerY < boundsB.centerY ? -1 : 1;
            const separation = overlapY / 2;
            
            if (!bodyA.isStatic) {
                bodyA.position.y += direction * separation;
            }
            if (!bodyB.isStatic) {
                bodyB.position.y -= direction * separation;
            }
            
            // 交换速度
            const relativeVelocity = bodyA.velocity.y - bodyB.velocity.y;
            const impulse = 2 * relativeVelocity / (bodyA.mass + bodyB.mass);
            
            if (!bodyA.isStatic) {
                bodyA.velocity.y -= impulse * bodyB.mass * bodyA.restitution;
            }
            if (!bodyB.isStatic) {
                bodyB.velocity.y += impulse * bodyA.mass * bodyB.restitution;
            }
        }
        
        // 计算伤害
        const impactForce = Math.sqrt(
            Math.pow(bodyA.velocity.x - bodyB.velocity.x, 2) + 
            Math.pow(bodyA.velocity.y - bodyB.velocity.y, 2)
        );
        
        const damage = impactForce * 5;
        
        if (damage > 3) {
            bodyA.takeDamage(damage);
            bodyB.takeDamage(damage);
        }
    }
    
    // 计算抛物线轨迹
    static calculateTrajectory(startPos, velocity, steps = 50) {
        const points = [];
        let pos = startPos.copy();
        let vel = velocity.copy();
        
        for (let i = 0; i < steps; i++) {
            points.push(pos.copy());
            
            vel.y += 0.5; // 重力
            vel = vel.multiply(0.999); // 空气阻力
            pos = pos.add(vel);
            
            // 如果到达地面，停止计算
            if (pos.y > 580) break;
        }
        
        return points;
    }
}
