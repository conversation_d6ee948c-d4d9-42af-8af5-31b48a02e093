// 游戏主类
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 游戏状态
        this.gameState = 'playing'; // playing, paused, gameOver, levelComplete
        this.score = 0;
        this.totalScore = 0;
        
        // 游戏对象
        this.levelManager = new LevelManager();
        this.targetManager = new TargetManager();
        this.slingshot = new Slingshot(150, 400);
        this.birds = [];
        this.currentBirdIndex = 0;
        this.activeBirds = []; // 飞行中的小鸟

        // 效果系统
        this.particleSystem = new ParticleSystem();
        this.soundManager = new SoundManager();
        this.screenShake = new ScreenShake();
        this.textEffectManager = new TextEffectManager();
        
        // 输入处理
        this.mousePos = new Physics.Vector2(0, 0);
        this.isMouseDown = false;
        
        // 游戏计时
        this.levelStartTime = Date.now();
        this.birdLaunchTime = 0;
        this.waitTime = 3000; // 等待时间（毫秒）
        
        // 初始化游戏
        this.initLevel();
        this.setupEventListeners();
        
        // 开始游戏循环
        this.gameLoop();
    }
    
    initLevel() {
        // 设置关卡
        this.levelManager.setupLevelTargets(this.targetManager);
        this.birds = this.levelManager.getLevelBirds();
        this.currentBirdIndex = 0;
        this.activeBirds = [];
        
        // 设置第一只小鸟
        if (this.birds.length > 0) {
            this.slingshot.setBird(this.birds[0]);
        }
        
        // 重置游戏状态
        this.gameState = 'playing';
        this.levelStartTime = Date.now();
        this.score = 0;
        
        // 更新UI
        this.updateUI();
    }
    
    setupEventListeners() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        this.mousePos = new Physics.Vector2(
            e.clientX - rect.left,
            e.clientY - rect.top
        );
        
        if (this.gameState === 'playing' && this.slingshot.isMouseInRange(this.mousePos)) {
            this.slingshot.startDrag(this.mousePos);
            this.isMouseDown = true;
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        this.mousePos = new Physics.Vector2(
            e.clientX - rect.left,
            e.clientY - rect.top
        );
        
        if (this.isMouseDown && this.gameState === 'playing') {
            this.slingshot.updateDrag(this.mousePos);
        }
    }
    
    handleMouseUp(e) {
        if (this.isMouseDown && this.gameState === 'playing') {
            const currentBird = this.slingshot.currentBird;
            this.slingshot.endDrag();
            this.isMouseDown = false;

            // 如果发射了小鸟，将其添加到activeBirds并准备下一只
            if (this.slingshot.currentBird === null && currentBird && currentBird.isLaunched) {
                this.activeBirds.push(currentBird);
                this.birdLaunchTime = Date.now();
                this.soundManager.playLaunch();
                this.prepareNextBird();
            }
        }
    }
    
    handleKeyDown(e) {
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                this.useSpecialAbility();
                break;
            case 'KeyR':
                this.reset();
                break;
            case 'KeyN':
                if (this.gameState === 'levelComplete') {
                    this.nextLevel();
                }
                break;
        }
    }
    
    useSpecialAbility() {
        // 对所有飞行中的小鸟使用特殊能力
        for (let i = this.activeBirds.length - 1; i >= 0; i--) {
            const bird = this.activeBirds[i];
            const newBirds = bird.useSpecialAbility();
            
            // 添加分裂出的小鸟
            if (newBirds && newBirds.length > 0) {
                this.activeBirds.push(...newBirds);
            }
        }
    }
    
    prepareNextBird() {
        this.currentBirdIndex++;
        
        if (this.currentBirdIndex < this.birds.length) {
            // 延迟设置下一只小鸟
            setTimeout(() => {
                if (this.gameState === 'playing') {
                    this.slingshot.setBird(this.birds[this.currentBirdIndex]);
                }
            }, 1000);
        }
    }
    
    update() {
        if (this.gameState !== 'playing') return;

        // 更新目标
        this.targetManager.update();
        this.targetManager.handleExplosions();

        // 更新飞行中的小鸟
        for (let i = this.activeBirds.length - 1; i >= 0; i--) {
            const bird = this.activeBirds[i];
            bird.update();

            // 移除已停止或被摧毁的小鸟
            if (!bird.isLaunched || bird.isDestroyed) {
                this.activeBirds.splice(i, 1);
            }
        }

        // 碰撞检测
        this.handleCollisions();

        // 更新效果系统
        this.particleSystem.update();
        this.screenShake.update();
        this.textEffectManager.update();

        // 检查游戏状态
        this.checkGameState();

        // 更新UI
        this.updateUI();
    }
    
    handleCollisions() {
        const targets = this.targetManager.getAllTargets();
        
        // 小鸟与目标的碰撞
        this.activeBirds.forEach(bird => {
            targets.forEach(target => {
                if (Physics.checkCollision(bird, target)) {
                    Physics.resolveCollision(bird, target);

                    // 创建撞击效果
                    const impactX = target.position.x + target.width / 2;
                    const impactY = target.position.y + target.height / 2;
                    this.particleSystem.createImpact(impactX, impactY);
                    this.soundManager.playHit();
                    this.screenShake.shake(3, 10);

                    // 计算得分
                    if (target.isDestroyed) {
                        this.score += target.points;
                        this.particleSystem.createDestruction(target.position.x, target.position.y, target.width, target.height, target.color);
                        this.particleSystem.createStars(impactX, impactY);
                        this.textEffectManager.addText(impactX, impactY - 20, '+' + target.points, '#FFD700', 16);
                        this.soundManager.playScore();
                        this.screenShake.shake(5, 15);
                    }
                }
            });
        });
        
        // 目标之间的碰撞
        for (let i = 0; i < targets.length; i++) {
            for (let j = i + 1; j < targets.length; j++) {
                if (Physics.checkCollision(targets[i], targets[j])) {
                    Physics.resolveCollision(targets[i], targets[j]);
                }
            }
        }
        
        // 处理爆炸伤害
        this.handleExplosionDamage();
    }
    
    handleExplosionDamage() {
        const targets = this.targetManager.getAllTargets();
        
        // 检查黑鸟爆炸
        this.activeBirds.forEach(bird => {
            if (bird.type === 'black' && bird.specialAbilityUsed && bird.explosionRadius) {
                const explosionX = bird.position.x + bird.width / 2;
                const explosionY = bird.position.y + bird.height / 2;

                // 只在第一次爆炸时创建效果
                if (bird.explosionRadius === 80) {
                    this.particleSystem.createExplosion(explosionX, explosionY, '#FF6600', 30);
                    this.soundManager.playExplosion();
                    this.screenShake.shake(8, 20);
                }

                targets.forEach(target => {
                    if (bird.isInExplosionRange(target)) {
                        target.takeDamage(bird.explosionDamage);
                        if (target.isDestroyed) {
                            this.score += target.points;
                            this.textEffectManager.addText(target.position.x + target.width/2, target.position.y, '+' + target.points, '#FFD700', 14);
                        }
                    }
                });
            }
        });
        
        // 检查TNT爆炸
        targets.forEach(exploder => {
            if (exploder.isExploding) {
                const explosionX = exploder.position.x + exploder.width / 2;
                const explosionY = exploder.position.y + exploder.height / 2;

                // 只在第一次爆炸时创建效果
                if (exploder.explosionTimer === 29) {
                    this.particleSystem.createExplosion(explosionX, explosionY, '#FF4500', 25);
                    this.soundManager.playExplosion();
                    this.screenShake.shake(10, 25);
                }

                targets.forEach(target => {
                    if (target !== exploder && exploder.isInExplosionRange(target)) {
                        target.takeDamage(30);
                        if (target.isDestroyed) {
                            this.score += target.points;
                            this.textEffectManager.addText(target.position.x + target.width/2, target.position.y, '+' + target.points, '#FFD700', 14);
                        }
                    }
                });

                // 对飞行中的小鸟也造成伤害
                this.activeBirds.forEach(bird => {
                    if (exploder.isInExplosionRange(bird)) {
                        bird.takeDamage(20);
                    }
                });
            }
        });
    }
    
    checkGameState() {
        // 检查是否胜利（所有猪被消灭）
        if (this.targetManager.allPigsDestroyed()) {
            this.levelComplete();
            return;
        }
        
        // 检查是否失败（没有小鸟了且还有猪存活）
        const hasActiveBirds = this.activeBirds.length > 0;
        const hasRemainingBirds = this.currentBirdIndex < this.birds.length;
        const birdInSlingshot = this.slingshot.currentBird !== null;
        
        if (!hasActiveBirds && !hasRemainingBirds && !birdInSlingshot) {
            // 等待一段时间后判定失败
            if (Date.now() - this.birdLaunchTime > this.waitTime) {
                this.gameOver();
            }
        }
    }
    
    levelComplete() {
        this.gameState = 'levelComplete';

        // 播放胜利音效
        this.soundManager.playVictory();

        // 创建庆祝效果
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                this.particleSystem.createStars(600 + Math.random() * 200, 300 + Math.random() * 100, 10);
            }, i * 200);
        }

        // 计算奖励分数
        const timeBonus = Math.max(0, 30000 - (Date.now() - this.levelStartTime));
        const birdBonus = (this.birds.length - this.currentBirdIndex) * 10000;

        this.score += Math.floor(timeBonus / 100) + birdBonus;
        this.totalScore += this.score;

        // 显示完成信息
        this.showLevelComplete();
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        this.soundManager.playGameOver();
        this.showGameOver();
    }
    
    showLevelComplete() {
        const gameOverDiv = document.getElementById('gameOver');
        const titleElement = document.getElementById('gameOverTitle');
        const messageElement = document.getElementById('gameOverMessage');
        
        titleElement.textContent = '关卡完成！';
        messageElement.innerHTML = `
            <p>得分: ${this.score}</p>
            <p>总分: ${this.totalScore}</p>
            <p>按 N 键进入下一关</p>
        `;
        
        gameOverDiv.style.display = 'block';
    }
    
    showGameOver() {
        const gameOverDiv = document.getElementById('gameOver');
        const titleElement = document.getElementById('gameOverTitle');
        const messageElement = document.getElementById('gameOverMessage');
        
        titleElement.textContent = '游戏结束';
        messageElement.innerHTML = `
            <p>本关得分: ${this.score}</p>
            <p>总分: ${this.totalScore}</p>
            <p>点击重新开始按钮再试一次</p>
        `;
        
        gameOverDiv.style.display = 'block';
    }
    
    draw() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 应用屏幕震动
        this.ctx.save();
        this.screenShake.apply(this.ctx);

        // 绘制背景
        this.levelManager.drawBackground(this.ctx);

        // 绘制弹弓
        this.slingshot.draw(this.ctx);

        // 绘制目标
        this.targetManager.draw(this.ctx);

        // 绘制小鸟
        this.activeBirds.forEach(bird => bird.draw(this.ctx));

        // 绘制弹弓上的小鸟
        if (this.slingshot.currentBird) {
            this.slingshot.currentBird.draw(this.ctx);
        }

        // 绘制粒子效果
        this.particleSystem.draw(this.ctx);

        // 重置屏幕震动
        this.screenShake.reset(this.ctx);
        this.ctx.restore();

        // 绘制文字效果（不受震动影响）
        this.textEffectManager.draw(this.ctx);

        // 绘制剩余小鸟
        this.drawRemainingBirds();

        // 绘制UI信息
        this.drawGameInfo();
    }
    
    drawRemainingBirds() {
        const startX = 50;
        const startY = 50;
        const spacing = 40;
        
        for (let i = this.currentBirdIndex + 1; i < this.birds.length; i++) {
            const bird = this.birds[i];
            const x = startX + (i - this.currentBirdIndex - 1) * spacing;
            
            this.ctx.fillStyle = bird.color;
            this.ctx.beginPath();
            this.ctx.arc(x, startY, 15, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 边框
            this.ctx.strokeStyle = 'white';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        }
    }
    
    drawGameInfo() {
        // 绘制特殊能力提示
        if (this.activeBirds.length > 0) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(10, this.canvas.height - 60, 200, 50);
            
            this.ctx.fillStyle = 'white';
            this.ctx.font = '14px Arial';
            this.ctx.fillText('按空格键使用特殊能力', 20, this.canvas.height - 35);
            this.ctx.fillText('R键重新开始', 20, this.canvas.height - 15);
        }
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('birds').textContent = Math.max(0, this.birds.length - this.currentBirdIndex);
        document.getElementById('level').textContent = this.levelManager.currentLevel;
    }
    
    // 公共方法
    reset() {
        document.getElementById('gameOver').style.display = 'none';
        this.particleSystem.clear();
        this.textEffectManager.clear();
        this.initLevel();
    }
    
    restart() {
        this.totalScore = 0;
        this.levelManager.reset();
        this.reset();
    }
    
    nextLevel() {
        if (this.levelManager.nextLevel()) {
            document.getElementById('gameOver').style.display = 'none';
            this.initLevel();
        } else {
            // 游戏完成
            const gameOverDiv = document.getElementById('gameOver');
            const titleElement = document.getElementById('gameOverTitle');
            const messageElement = document.getElementById('gameOverMessage');
            
            titleElement.textContent = '恭喜通关！';
            messageElement.innerHTML = `
                <p>最终得分: ${this.totalScore}</p>
                <p>你已经完成了所有关卡！</p>
            `;
        }
    }
    
    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}
