<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Test</h1>
    <div id="output"></div>
    
    <script>
        console.log('Starting JavaScript test...');
        
        try {
            // 测试加载所有脚本
            const scripts = [
                'js/physics.js',
                'js/bird.js', 
                'js/slingshot.js',
                'js/targets.js',
                'js/levels.js',
                'js/game.js',
                'js/main.js'
            ];
            
            let loadedCount = 0;
            
            scripts.forEach((src, index) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`Loaded: ${src}`);
                    loadedCount++;
                    if (loadedCount === scripts.length) {
                        console.log('All scripts loaded successfully!');
                        document.getElementById('output').innerHTML = 'All scripts loaded successfully!';
                    }
                };
                script.onerror = (e) => {
                    console.error(`Error loading: ${src}`, e);
                    document.getElementById('output').innerHTML += `<br>Error loading: ${src}`;
                };
                document.head.appendChild(script);
            });
            
        } catch (e) {
            console.error('Test error:', e);
            document.getElementById('output').innerHTML = 'Test error: ' + e.message;
        }
    </script>
</body>
</html>
