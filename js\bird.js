// 小鸟类
class Bird extends Physics.Body {
    constructor(x, y, type = 'red') {
        super(x, y, 30, 30, 1);
        this.type = type;
        this.isLaunched = false;
        this.trail = [];
        this.maxTrailLength = 20;
        this.specialAbilityUsed = false;
        this.angle = 0;
        this.originalPosition = new Physics.Vector2(x, y);
        
        // 不同类型小鸟的属性
        this.setTypeProperties();
    }
    
    setTypeProperties() {
        switch(this.type) {
            case 'red':
                this.color = '#FF4444';
                this.damage = 20;
                this.description = '红鸟 - 基础攻击';
                break;
            case 'blue':
                this.color = '#4444FF';
                this.damage = 15;
                this.description = '蓝鸟 - 分裂攻击';
                break;
            case 'yellow':
                this.color = '#FFFF44';
                this.damage = 25;
                this.description = '黄鸟 - 加速攻击';
                break;
            case 'black':
                this.color = '#444444';
                this.damage = 35;
                this.description = '黑鸟 - 爆炸攻击';
                break;
            case 'white':
                this.color = '#FFFFFF';
                this.damage = 30;
                this.description = '白鸟 - 投蛋攻击';
                break;
        }
    }
    
    launch(velocity) {
        if (!this.isLaunched) {
            this.isLaunched = true;
            this.velocity = velocity.copy();
            this.trail = [];
        }
    }
    
    update() {
        if (this.isLaunched && !this.isDestroyed) {
            // 更新轨迹
            if (this.trail.length >= this.maxTrailLength) {
                this.trail.shift();
            }
            this.trail.push(this.position.copy());
            
            // 更新角度（根据速度方向）
            if (this.velocity.magnitude() > 0.1) {
                this.angle = Math.atan2(this.velocity.y, this.velocity.x);
            }
            
            // 调用父类更新
            super.update();
            
            // 检查是否停止运动
            if (this.velocity.magnitude() < 0.5 && this.position.y >= 580 - this.height) {
                this.isLaunched = false;
            }
        }
    }
    
    // 特殊能力
    useSpecialAbility() {
        if (this.isLaunched && !this.specialAbilityUsed) {
            this.specialAbilityUsed = true;
            
            switch(this.type) {
                case 'blue':
                    return this.splitBird();
                case 'yellow':
                    this.speedBoost();
                    break;
                case 'black':
                    this.explode();
                    break;
                case 'white':
                    return this.dropEgg();
            }
        }
        return [];
    }
    
    splitBird() {
        // 蓝鸟分裂成3只小鸟
        const splitBirds = [];
        const angles = [-0.3, 0, 0.3];
        
        for (let i = 0; i < 3; i++) {
            const newBird = new Bird(this.position.x, this.position.y, 'blue');
            const speed = this.velocity.magnitude() * 0.8;
            const angle = Math.atan2(this.velocity.y, this.velocity.x) + angles[i];
            
            newBird.velocity = new Physics.Vector2(
                Math.cos(angle) * speed,
                Math.sin(angle) * speed
            );
            newBird.isLaunched = true;
            newBird.specialAbilityUsed = true;
            newBird.width = 20;
            newBird.height = 20;
            newBird.damage = 10;
            
            splitBirds.push(newBird);
        }
        
        this.isDestroyed = true;
        return splitBirds;
    }
    
    speedBoost() {
        // 黄鸟加速
        const currentSpeed = this.velocity.magnitude();
        const direction = this.velocity.normalize();
        this.velocity = direction.multiply(currentSpeed * 1.5);
    }
    
    explode() {
        // 黑鸟爆炸
        this.explosionRadius = 80;
        this.explosionDamage = 50;
        // 爆炸效果将在游戏主循环中处理
    }
    
    dropEgg() {
        // 白鸟投蛋
        const egg = new Bird(this.position.x, this.position.y + 10, 'white');
        egg.velocity = new Physics.Vector2(0, 5);
        egg.isLaunched = true;
        egg.specialAbilityUsed = true;
        egg.width = 15;
        egg.height = 20;
        egg.damage = 25;
        egg.color = '#FFFFCC';
        
        // 白鸟本身向上飞
        this.velocity.y -= 3;
        
        return [egg];
    }
    
    draw(ctx) {
        if (this.isDestroyed) return;
        
        // 绘制轨迹
        if (this.isLaunched && this.trail.length > 1) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let i = 0; i < this.trail.length; i++) {
                const alpha = i / this.trail.length;
                const point = this.trail[i];
                
                if (i === 0) {
                    ctx.moveTo(point.x + this.width/2, point.y + this.height/2);
                } else {
                    ctx.lineTo(point.x + this.width/2, point.y + this.height/2);
                }
            }
            ctx.stroke();
        }
        
        // 保存画布状态
        ctx.save();
        
        // 移动到小鸟中心
        ctx.translate(this.position.x + this.width/2, this.position.y + this.height/2);
        
        // 旋转小鸟
        if (this.isLaunched) {
            ctx.rotate(this.angle);
        }
        
        // 绘制小鸟身体
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制小鸟眼睛
        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.arc(-5, -5, 6, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = 'black';
        ctx.beginPath();
        ctx.arc(-3, -5, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制小鸟嘴巴
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.moveTo(8, 0);
        ctx.lineTo(15, -3);
        ctx.lineTo(15, 3);
        ctx.closePath();
        ctx.fill();
        
        // 绘制小鸟眉毛（愤怒表情）
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(-10, -10);
        ctx.lineTo(-2, -8);
        ctx.stroke();
        
        // 恢复画布状态
        ctx.restore();
        
        // 绘制爆炸效果
        if (this.type === 'black' && this.specialAbilityUsed && this.explosionRadius) {
            ctx.strokeStyle = 'rgba(255, 100, 0, 0.5)';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(this.position.x + this.width/2, this.position.y + this.height/2, this.explosionRadius, 0, Math.PI * 2);
            ctx.stroke();
            
            // 减少爆炸半径
            this.explosionRadius -= 2;
            if (this.explosionRadius <= 0) {
                this.explosionRadius = null;
            }
        }
    }
    
    reset() {
        this.position = this.originalPosition.copy();
        this.velocity = new Physics.Vector2(0, 0);
        this.acceleration = new Physics.Vector2(0, 0);
        this.isLaunched = false;
        this.isDestroyed = false;
        this.specialAbilityUsed = false;
        this.trail = [];
        this.angle = 0;
        this.health = this.maxHealth;
    }
    
    // 检查是否在爆炸范围内
    isInExplosionRange(target) {
        if (this.type === 'black' && this.explosionRadius) {
            const distance = this.position.distance(target.position);
            return distance <= this.explosionRadius;
        }
        return false;
    }
}
