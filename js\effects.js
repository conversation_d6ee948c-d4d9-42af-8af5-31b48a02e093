// 粒子效果系统
class Particle {
    constructor(x, y, vx, vy, color, size, life) {
        this.position = new Physics.Vector2(x, y);
        this.velocity = new Physics.Vector2(vx, vy);
        this.color = color;
        this.size = size;
        this.life = life;
        this.maxLife = life;
        this.gravity = 0.1;
        this.friction = 0.98;
    }
    
    update() {
        this.velocity.y += this.gravity;
        this.velocity = this.velocity.multiply(this.friction);
        this.position = this.position.add(this.velocity);
        this.life--;
        
        return this.life > 0;
    }
    
    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
    }
    
    // 创建爆炸效果
    createExplosion(x, y, color = '#FF6600', count = 20) {
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = Math.random() * 5 + 2;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const size = Math.random() * 4 + 2;
            const life = Math.random() * 30 + 20;
            
            this.particles.push(new Particle(x, y, vx, vy, color, size, life));
        }
    }
    
    // 创建破坏效果
    createDestruction(x, y, width, height, color = '#8B4513') {
        const count = Math.min(15, (width * height) / 100);
        
        for (let i = 0; i < count; i++) {
            const px = x + Math.random() * width;
            const py = y + Math.random() * height;
            const vx = (Math.random() - 0.5) * 6;
            const vy = Math.random() * -3 - 1;
            const size = Math.random() * 3 + 1;
            const life = Math.random() * 40 + 30;
            
            this.particles.push(new Particle(px, py, vx, vy, color, size, life));
        }
    }
    
    // 创建撞击效果
    createImpact(x, y, color = '#FFFFFF') {
        for (let i = 0; i < 10; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 3 + 1;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const size = Math.random() * 2 + 1;
            const life = Math.random() * 20 + 10;
            
            this.particles.push(new Particle(x, y, vx, vy, color, size, life));
        }
    }
    
    // 创建星星效果（得分时）
    createStars(x, y, count = 5) {
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 2 + 1;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed - 2;
            const size = Math.random() * 3 + 2;
            const life = Math.random() * 50 + 40;
            
            this.particles.push(new Particle(x, y, vx, vy, '#FFD700', size, life));
        }
    }
    
    update() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            if (!this.particles[i].update()) {
                this.particles.splice(i, 1);
            }
        }
    }
    
    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }
    
    clear() {
        this.particles = [];
    }
}

// 音效管理器
class SoundManager {
    constructor() {
        this.sounds = {};
        this.enabled = true;
        this.volume = 0.5;
        
        // 创建音效（使用Web Audio API生成简单音效）
        this.audioContext = null;
        this.initAudioContext();
    }
    
    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.enabled = false;
        }
    }
    
    // 播放发射音效
    playLaunch() {
        if (!this.enabled) return;
        this.playTone(200, 0.1, 'sine');
    }
    
    // 播放撞击音效
    playHit() {
        if (!this.enabled) return;
        this.playTone(150, 0.1, 'square');
    }
    
    // 播放爆炸音效
    playExplosion() {
        if (!this.enabled) return;
        this.playNoise(0.2);
    }
    
    // 播放得分音效
    playScore() {
        if (!this.enabled) return;
        this.playTone(400, 0.2, 'sine');
        setTimeout(() => this.playTone(600, 0.2, 'sine'), 100);
    }
    
    // 播放胜利音效
    playVictory() {
        if (!this.enabled) return;
        const notes = [262, 330, 392, 523]; // C, E, G, C
        notes.forEach((freq, i) => {
            setTimeout(() => this.playTone(freq, 0.3, 'sine'), i * 150);
        });
    }
    
    // 播放失败音效
    playGameOver() {
        if (!this.enabled) return;
        this.playTone(200, 0.5, 'sawtooth');
    }
    
    // 播放纯音
    playTone(frequency, duration, type = 'sine') {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = type;
        
        gainNode.gain.setValueAtTime(this.volume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
    
    // 播放噪音（爆炸效果）
    playNoise(duration) {
        if (!this.audioContext) return;
        
        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < bufferSize; i++) {
            data[i] = Math.random() * 2 - 1;
        }
        
        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        
        source.buffer = buffer;
        source.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        gainNode.gain.setValueAtTime(this.volume * 0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
        
        source.start(this.audioContext.currentTime);
    }
    
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
    }
    
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}

// 屏幕震动效果
class ScreenShake {
    constructor() {
        this.intensity = 0;
        this.duration = 0;
        this.offset = new Physics.Vector2(0, 0);
    }
    
    shake(intensity, duration) {
        this.intensity = Math.max(this.intensity, intensity);
        this.duration = Math.max(this.duration, duration);
    }
    
    update() {
        if (this.duration > 0) {
            this.duration--;
            const currentIntensity = this.intensity * (this.duration / 30);
            this.offset.x = (Math.random() - 0.5) * currentIntensity;
            this.offset.y = (Math.random() - 0.5) * currentIntensity;
        } else {
            this.offset.x = 0;
            this.offset.y = 0;
            this.intensity = 0;
        }
    }
    
    apply(ctx) {
        if (this.intensity > 0) {
            ctx.translate(this.offset.x, this.offset.y);
        }
    }
    
    reset(ctx) {
        if (this.intensity > 0) {
            ctx.translate(-this.offset.x, -this.offset.y);
        }
    }
}

// 文字动画效果
class FloatingText {
    constructor(x, y, text, color = '#FFFFFF', size = 20) {
        this.position = new Physics.Vector2(x, y);
        this.velocity = new Physics.Vector2(0, -1);
        this.text = text;
        this.color = color;
        this.size = size;
        this.life = 60;
        this.maxLife = 60;
    }
    
    update() {
        this.position = this.position.add(this.velocity);
        this.velocity.y -= 0.02;
        this.life--;
        return this.life > 0;
    }
    
    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.font = `bold ${this.size}px Arial`;
        ctx.textAlign = 'center';
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.strokeText(this.text, this.position.x, this.position.y);
        ctx.fillText(this.text, this.position.x, this.position.y);
        ctx.restore();
    }
}

class TextEffectManager {
    constructor() {
        this.texts = [];
    }
    
    addText(x, y, text, color, size) {
        this.texts.push(new FloatingText(x, y, text, color, size));
    }
    
    update() {
        for (let i = this.texts.length - 1; i >= 0; i--) {
            if (!this.texts[i].update()) {
                this.texts.splice(i, 1);
            }
        }
    }
    
    draw(ctx) {
        this.texts.forEach(text => text.draw(ctx));
    }
    
    clear() {
        this.texts = [];
    }
}
