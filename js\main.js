// 游戏入口文件
let game;

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化游戏
    game = new Game();
    
    // 显示游戏说明
    showGameInstructions();
});

// 显示游戏说明
function showGameInstructions() {
    const instructions = `
        🎮 愤怒的小鸟 - 游戏说明 🎮
        
        🎯 游戏目标：
        • 用弹弓发射小鸟，摧毁所有绿色的猪
        • 每个关卡都有不同的结构和挑战
        
        🐦 小鸟类型：
        • 红鸟：基础攻击，平衡的伤害和速度
        • 蓝鸟：按空格键分裂成3只小鸟
        • 黄鸟：按空格键加速冲刺
        • 黑鸟：按空格键爆炸，造成范围伤害
        • 白鸟：按空格键投下炸蛋
        
        🎮 操作方法：
        • 鼠标拖拽：瞄准和调整发射力度
        • 空格键：使用小鸟的特殊能力
        • R键：重新开始当前关卡
        • N键：进入下一关（关卡完成后）
        
        🏗️ 障碍物：
        • 木块：容易破坏，伤害较低
        • 石块：坚固，需要更大力量
        • 冰块：脆弱但滑溜
        • TNT：爆炸后造成范围伤害
        
        💡 游戏技巧：
        • 观察结构的薄弱点
        • 合理利用物理效果
        • 善用特殊能力
        • TNT可以引发连锁爆炸
        
        祝你游戏愉快！🎉
    `;
    
    console.log(instructions);
    
    // 可以选择显示一个模态框
    // alert(instructions);
}

// 全局游戏控制函数
window.gameControls = {
    reset: function() {
        if (game) {
            game.reset();
        }
    },
    
    restart: function() {
        if (game) {
            game.restart();
        }
    },
    
    nextLevel: function() {
        if (game) {
            game.nextLevel();
        }
    },
    
    useSpecialAbility: function() {
        if (game) {
            game.useSpecialAbility();
        }
    }
};

// 性能监控
let lastTime = 0;
let frameCount = 0;
let fps = 0;

function updateFPS() {
    const now = performance.now();
    frameCount++;
    
    if (now - lastTime >= 1000) {
        fps = Math.round((frameCount * 1000) / (now - lastTime));
        frameCount = 0;
        lastTime = now;
        
        // 在控制台显示FPS（可选）
        // console.log('FPS:', fps);
    }
    
    requestAnimationFrame(updateFPS);
}

// 开始FPS监控
updateFPS();

// 错误处理
window.addEventListener('error', function(e) {
    console.error('游戏错误:', e.error);
    
    // 可以在这里添加错误恢复逻辑
    if (game && game.gameState === 'playing') {
        console.log('尝试恢复游戏状态...');
        // game.reset();
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (game) {
        if (document.hidden) {
            // 页面隐藏时暂停游戏
            if (game.gameState === 'playing') {
                game.gameState = 'paused';
            }
        } else {
            // 页面显示时恢复游戏
            if (game.gameState === 'paused') {
                game.gameState = 'playing';
            }
        }
    }
});

// 调试模式
const DEBUG_MODE = false;

if (DEBUG_MODE) {
    // 添加调试信息
    window.debugInfo = {
        getGameState: () => game ? game.gameState : 'not initialized',
        getCurrentLevel: () => game ? game.levelManager.currentLevel : 'not initialized',
        getBirdCount: () => game ? game.birds.length : 'not initialized',
        getScore: () => game ? game.score : 'not initialized',
        getTargetCount: () => game ? game.targetManager.targets.length : 'not initialized'
    };
    
    console.log('调试模式已启用');
    console.log('使用 window.debugInfo 查看游戏状态');
}
