# 愤怒的小鸟 - HTML5游戏

一个使用纯HTML5、CSS3和JavaScript开发的愤怒的小鸟游戏，具有完整的物理引擎、粒子效果和音效系统。

## 🎮 游戏特色

### 核心功能
- **完整的物理引擎**：重力、碰撞检测、抛物线运动
- **多种小鸟类型**：每种小鸟都有独特的特殊能力
- **丰富的障碍物**：木块、石块、冰块、TNT等不同材质
- **6个精心设计的关卡**：难度递增，挑战性十足
- **实时粒子效果**：爆炸、破坏、撞击等视觉效果
- **动态音效系统**：发射、撞击、爆炸、得分音效

### 小鸟类型
1. **红鸟** 🔴 - 基础攻击，平衡的伤害和速度
2. **蓝鸟** 🔵 - 按空格键分裂成3只小鸟
3. **黄鸟** 🟡 - 按空格键加速冲刺
4. **黑鸟** ⚫ - 按空格键爆炸，造成范围伤害
5. **白鸟** ⚪ - 按空格键投下炸蛋

### 障碍物类型
- **木块** 🟫 - 容易破坏，伤害较低
- **石块** 🪨 - 坚固，需要更大力量
- **冰块** 🧊 - 脆弱但滑溜
- **TNT** 💥 - 爆炸后造成范围伤害
- **猪** 🐷 - 游戏目标，消灭所有猪即可过关

## 🎯 操作方法

### 基本操作
- **鼠标拖拽**：瞄准和调整发射力度
- **空格键**：使用小鸟的特殊能力
- **R键**：重新开始当前关卡
- **N键**：进入下一关（关卡完成后）

### 游戏界面
- **力度指示器**：显示当前发射力度
- **轨迹预测**：红色虚线显示瞄准方向，黄色轨迹显示预测路径
- **得分显示**：实时显示当前得分和剩余小鸟数量
- **音效控制**：点击🔊按钮切换音效开关

## 🏗️ 技术架构

### 文件结构
```
├── index.html          # 主页面
├── js/
│   ├── physics.js      # 物理引擎
│   ├── bird.js         # 小鸟类
│   ├── slingshot.js    # 弹弓系统
│   ├── targets.js      # 目标和障碍物
│   ├── levels.js       # 关卡系统
│   ├── effects.js      # 粒子效果和音效
│   ├── game.js         # 游戏主逻辑
│   └── main.js         # 入口文件
└── README.md           # 说明文档
```

### 核心系统

#### 物理引擎 (physics.js)
- **Vector2类**：二维向量运算
- **Body类**：物理体基类，包含位置、速度、加速度
- **碰撞检测**：AABB包围盒碰撞检测
- **碰撞响应**：弹性碰撞和摩擦力模拟

#### 粒子系统 (effects.js)
- **爆炸效果**：径向粒子爆炸
- **破坏效果**：物体破碎粒子
- **撞击效果**：碰撞火花
- **得分效果**：金色星星粒子

#### 音效系统 (effects.js)
- **Web Audio API**：生成程序化音效
- **多种音效**：发射、撞击、爆炸、得分、胜利、失败
- **音量控制**：可调节音量和开关

## 🎲 游戏机制

### 得分系统
- **猪**：5000分
- **木块**：500分
- **石块**：1000分
- **冰块**：300分
- **TNT**：2000分
- **时间奖励**：剩余时间转换为分数
- **小鸟奖励**：剩余小鸟每只10000分

### 物理特性
- **重力**：所有物体受重力影响
- **弹性**：不同材质有不同弹性系数
- **摩擦力**：地面和空气阻力
- **质量**：影响碰撞响应和运动

### 特殊机制
- **连锁爆炸**：TNT可以引发连锁反应
- **结构倒塌**：合理的物理模拟让结构真实倒塌
- **小鸟特能**：每种小鸟的独特能力增加策略性

## 🚀 运行方法

### 本地运行
1. 下载所有文件到本地目录
2. 使用现代浏览器打开 `index.html`
3. 开始游戏！

### HTTP服务器运行（推荐）
```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

然后访问 `http://localhost:8000`

## 🎨 游戏技巧

1. **观察结构**：寻找结构的薄弱点
2. **合理利用物理**：利用重力和惯性
3. **善用特殊能力**：在合适的时机使用空格键
4. **连锁反应**：利用TNT制造连锁爆炸
5. **角度控制**：调整发射角度以获得最佳轨迹

## 🔧 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

需要支持：
- HTML5 Canvas
- ES6 Classes
- Web Audio API（音效）

## 📝 开发说明

这是一个纯前端游戏，不需要服务器端支持。所有的游戏逻辑、物理计算和效果渲染都在客户端完成。

### 性能优化
- 对象池管理粒子
- 高效的碰撞检测算法
- 帧率监控和优化
- 内存管理和垃圾回收优化

### 扩展性
代码结构清晰，易于扩展：
- 添加新的小鸟类型
- 创建新的关卡
- 增加新的障碍物类型
- 添加新的特效

## 🎉 享受游戏！

这个愤怒的小鸟游戏完全使用Web技术开发，展示了现代浏览器的强大能力。希望你能享受游戏的乐趣！

如果你喜欢这个游戏，欢迎分享给朋友们一起玩！
